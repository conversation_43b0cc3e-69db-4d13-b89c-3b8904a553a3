<!doctype html><html lang=en class="js csstransforms3d"><head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=generator content="Hugo 0.134.3"><meta name=description content><meta name=author content="<EMAIL>"><link rel=icon href=/images/favicon.png type=image/png><title>Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions :: AWS System Manager</title>
<link href=/css/nucleus.css?1751838762 rel=stylesheet><link href=/css/fontawesome-all.min.css?1751838762 rel=stylesheet><link href=/css/hybrid.css?1751838762 rel=stylesheet><link href=/css/featherlight.min.css?1751838762 rel=stylesheet><link href=/css/perfect-scrollbar.min.css?1751838762 rel=stylesheet><link href=/css/auto-complete.css?1751838762 rel=stylesheet><link href=/css/atom-one-dark-reasonable.css?1751838762 rel=stylesheet><link href=/css/theme.css?1751838762 rel=stylesheet><link href=/css/hugo-theme.css?1751838762 rel=stylesheet><link href=/css/theme-workshop.css?1751838762 rel=stylesheet><script src=/js/jquery-3.3.1.min.js?1751838762></script><style>:root #header+#content>#left>#rlblock_left{display:none!important}</style></head><body data-url=/5-deployfrontend/5.2-enable-static-hosting/><nav id=sidebar class=showVisitedLinks><div id=header-wrapper><div id=header><a id=logo href=/><svg id="Layer_1" data-name="Layer 1" viewBox="0 0 60 30" width="30%"><defs><style>.cls-1{fill:#fff}.cls-2{fill:#f90;fill-rule:evenodd}</style></defs><title>AWS-Logo_White-Color</title><path class="cls-1" d="M14.09 10.85a4.7 4.7.0 00.19 1.48 7.73 7.73.0 00.54 **********.0 ***********.64.0 01-.32.49l-1 .7a.83.83.0 01-.***********.0 01-.49-.23 3.8 3.8.0 01-.6-.77q-.25-.42-.51-1a6.14 6.14.0 01-4.89 2.3 4.54 4.54.0 01-3.32-1.19 4.27 4.27.0 01-1.22-3.2 4.28 4.28.0 011.46-3.4A6.06 6.06.0 017.69 6.46a12.47 12.47.0 011.76.13q.92.13 1.91.36V5.73a3.65 3.65.0 00-.79-2.66A3.81 3.81.0 007.86 2.3a7.71 7.71.0 00-1.79.22 12.78 12.78.0 00-1.79.57 4.55 4.55.0 01-.58.22h-.26q-.35.0-.35-.52V2a1.09 1.09.0 01.12-.58 1.2 1.2.0 01.47-.35A10.88 10.88.0 015.77.32 10.19 10.19.0 018.36.0a6 6 0 014.35 1.35 5.49 5.49.0 011.38 4.09zM7.34 13.38a5.36 5.36.0 001.72-.31A3.63 3.63.0 0010.63 12 2.62 2.62.0 0011.19 11a5.63 5.63.0 00.16-1.44v-.7a14.35 14.35.0 00-1.53-.28 12.37 12.37.0 00-1.56-.1 3.84 3.84.0 00-2.47.67A2.34 2.34.0 005 11a2.35 2.35.0 00.61 1.76A2.4 2.4.0 007.34 13.38zm13.35 1.8a1 1 0 01-.64-.16 1.3 1.3.0 01-.35-.65L15.81 1.51a3 3 0 01-.15-.67.36.36.0 01.41-.41H17.7a1 1 0 01.65.16 1.4 1.4.0 01.33.65l2.79 11 2.59-11A1.17 1.17.0 0124.39.6a1.1 1.1.0 01.67-.16H26.4a1.1 1.1.0 01.67.16 1.17 1.17.0 01.32.65L30 12.39 32.88 1.25A1.39 1.39.0 0133.22.6a1 1 0 01.65-.16h1.54a.36.36.0 01.41.41 1.36 1.36.0 010 .26 3.64 3.64.0 01-.12.41l-4 12.86a1.3 1.3.0 01-.35.65 1 1 0 01-.64.16H29.25a1 1 0 01-.67-.17 1.26 1.26.0 01-.32-.67L25.67 3.64l-2.56 10.7a1.26 1.26.0 01-.32.67 1 1 0 01-.67.17zm21.36.44a11.28 11.28.0 01-2.56-.29 7.44 7.44.0 01-1.92-.67 1 1 0 01-.61-.93v-.84q0-.52.38-.52a.9.9.0 01.31.06l.42.17a8.77 8.77.0 001.83.58 9.78 9.78.0 002 .2 4.48 4.48.0 002.43-.55 1.76 1.76.0 00.86-1.57 1.61 1.61.0 00-.45-1.16A4.29 4.29.0 0043 9.22l-2.41-.76A5.15 5.15.0 0138 6.78a3.94 3.94.0 01-.83-2.41 3.7 3.7.0 01.45-1.85 4.47 4.47.0 011.19-1.37 5.27 5.27.0 011.7-.86A7.4 7.4.0 0142.6.0a8.87 8.87.0 011.12.07q.57.07 1.08.19t.95.26a4.27 4.27.0 01.7.29 1.59 1.59.0 01.49.41.94.94.0 01.15.55v.79q0 .52-.38.52a1.76 1.76.0 01-.64-.2 7.74 7.74.0 00-3.2-.64 4.37 4.37.0 00-2.21.47 1.6 1.6.0 00-.79 1.48 1.58 1.58.0 00.49 1.18 4.94 4.94.0 001.83.92L44.55 7a5.08 5.08.0 012.57 1.6A3.76 3.76.0 0147.9 11a4.21 4.21.0 01-.44 1.93 4.4 4.4.0 01-1.21 1.47 5.43 5.43.0 01-1.85.93A8.25 8.25.0 0142.05 15.62z"/><path class="cls-2" d="M45.19 23.81C39.72 27.85 31.78 30 25 30A36.64 36.64.0 01.22 20.57c-.51-.46-.06-1.09.56-.74A49.78 49.78.0 0025.53 26.4 49.23 49.23.0 0044.4 22.53C45.32 22.14 46.1 23.14 45.19 23.81z"/><path class="cls-2" d="M47.47 21.21c-.7-.9-4.63-.42-6.39-.21-.53.06-.62-.4-.14-.74 3.13-2.2 8.27-1.57 8.86-.83s-.16 5.89-3.09 8.35c-.45.38-.88.18-.68-.32C46.69 25.8 48.17 22.11 47.47 21.21z"/></svg></a></div><div class=searchbox><label for=search-by><i class="fas fa-search"></i></label>
<input data-search-input id=search-by type=search placeholder=Search...>
<span data-search-clear><i class="fas fa-times"></i></span></div><script type=text/javascript src=/js/lunr.min.js?1751838762></script><script type=text/javascript src=/js/auto-complete.js?1751838762></script><script type=text/javascript>var baseurl="https://thuananwork.github.io/"</script><script type=text/javascript src=/js/search.js?1751838762></script></div><div class=highlightable><ul class=topics><li data-nav-id=/1-introduce/ title=Introduction class=dd-item><a href=/1-introduce/><b>1. </b>Introduction
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/2-prerequiste/ title=Preparation class=dd-item><a href=/2-prerequiste/><b>2 </b>Preparation
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/2-prerequiste/2.1-installnodejs/ title="Installing NodeJS" class=dd-item><a href=/2-prerequiste/2.1-installnodejs/><b>2.1 </b>Installing NodeJS
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/2-prerequiste/2.2-installyarn/ title="Installing Yarn" class=dd-item><a href=/2-prerequiste/2.2-installyarn/><b>2.2 </b>Installing Yarn
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/2-prerequiste/2.3-installsamcli/ title="Installing SAM CLI" class=dd-item><a href=/2-prerequiste/2.3-installsamcli/><b>2.3 </b>Installing SAM CLI
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/2-prerequiste/2.4-createiam/ title="Create Account and Configure IAM" class=dd-item><a href=/2-prerequiste/2.4-createiam/><b>2.4 </b>Create Account and Configure IAM
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/2-prerequiste/2.5-create-google-oauth2/ title="Create Google OAuth2 Project" class=dd-item><a href=/2-prerequiste/2.5-create-google-oauth2/><b>2.5 </b>Create Google OAuth2 Project
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/3-deploybackend/ title="Deploying the Backend" class=dd-item><a href=/3-deploybackend/><b>3. </b>Deploying the Backend
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/3-deploybackend/3.1-deploy-backend/ title="Deploying the Backend with SAM CLI/CloudFormation" class=dd-item><a href=/3-deploybackend/3.1-deploy-backend/><b>3.1 </b>Deploying the Backend with SAM CLI/CloudFormation
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/3-deploybackend/3.2-check-status-log-backend/ title="Checking Backend Status and Logs After Deployment" class=dd-item><a href=/3-deploybackend/3.2-check-status-log-backend/><b>3.2 </b>Checking Backend Status and Logs After Deployment
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/4-testbackendapi/ title="Testing Backend APIs with Postman" class=dd-item><a href=/4-testbackendapi/><b>4 </b>Testing Backend APIs with Postman
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/4-testbackendapi/4.1-endpoint-api-gateway/ title="Retrieving the API Gateway Endpoint" class=dd-item><a href=/4-testbackendapi/4.1-endpoint-api-gateway/><b>4.1 </b>Retrieving the API Gateway Endpoint
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/4-testbackendapi/4.2-get-post-response/ title="Sending GET/POST Requests to Verify Backend Responses" class=dd-item><a href=/4-testbackendapi/4.2-get-post-response/><b>4.2 </b>Sending GET/POST Requests to Verify Backend Responses
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/5-deployfrontend/ title="Deploy Frontend" class="dd-item
parent"><a href=/5-deployfrontend/><b>5 </b>Deploy Frontend
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/5-deployfrontend/5.1-frontend-s3/ title="Deploy Frontend to S3 Bucket" class=dd-item><a href=/5-deployfrontend/5.1-frontend-s3/><b>5.1 </b>Deploy Frontend to S3 Bucket
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/5-deployfrontend/5.2-enable-static-hosting/ title="Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions" class="dd-item
active"><a href=/5-deployfrontend/5.2-enable-static-hosting/><b>5.2 </b>Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/5-deployfrontend/5.3-s3-bucket-permission/ title="Grant Public Permissions to S3 Bucket" class=dd-item><a href=/5-deployfrontend/5.3-s3-bucket-permission/><b>5.3 </b>Grant Public Permissions to S3 Bucket
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/5-deployfrontend/5.4-clientid-clientserver/ title="Configure Google OAuth2 Client ID and Client Secret" class=dd-item><a href=/5-deployfrontend/5.4-clientid-clientserver/><b>5.4 </b>Configure Google OAuth2 Client ID and Client Secret
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/5-deployfrontend/5.5-connect-frontend-api-backend/ title="Connect the Frontend to the Backend API" class=dd-item><a href=/5-deployfrontend/5.5-connect-frontend-api-backend/><b>5.5 </b>Connect the Frontend to the Backend API
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/6-ssl-s3-static/ title="Setup SSL S3 Static Website" class=dd-item><a href=/6-ssl-s3-static/><b>6. </b>Setup SSL S3 Static Website
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/7-demo/ title="Demo and Run the Project" class=dd-item><a href=/7-demo/><b>7. </b>Demo and Run the Project
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/8-cleanup/ title="Clean Up Resources" class=dd-item><a href=/8-cleanup/><b>8. </b>Clean Up Resources
<i class="fas fa-check read-icon"></i></a></li></ul><section id=shortcuts><h3>More</h3><ul><li><a class=padding href=https://www.facebook.com/groups/awsstudygroupfcj/><i class='fab fa-facebook'></i> AWS Study Group</a></li></ul></section><section id=prefooter><hr><ul><li><a class=padding><i class="fas fa-language fa-fw"></i><div class=select-style><select id=select-language onchange="location=this.value"><option id=en value=https://thuananwork.github.io/5-deployfrontend/5.2-enable-static-hosting/ selected>English</option><option id=vi value=https://thuananwork.github.io/vi/5-deployfrontend/5.2-enable-static-hosting/>Tiếng Việt</option></select><svg id="Capa_1" xmlns:xlink="http://www.w3.org/1999/xlink" width="255" height="255" viewBox="0 0 255 255" style="enable-background:new 0 0 255 255"><g><g id="arrow-drop-down"><polygon points="0,63.75 127.5,191.25 255,63.75"/></g></g></svg></div></a></li><li><a class=padding href=# data-clear-history-toggle><i class="fas fa-history fa-fw"></i> Clear History</a></li></ul></section><section id=footer><left><b>Workshop</b><br><img src="https://hitwebcounter.com/counter/counter.php?page=7920860&style=0038&nbdigits=9&type=page&initCount=0" title=Migrate alt="web counter" border=0></a><br><b><a href=https://cloudjourney.awsstudygroup.com/>Cloud Journey</a></b><br><img src="https://hitwebcounter.com/counter/counter.php?page=7830807&style=0038&nbdigits=9&type=page&initCount=0" title="Total CLoud Journey" alt="web counter" border=0>
</left><left><br><br><b>Last Updated</b><br><i><font color=orange>30-01-2022</font></i>
</left><left><br><br><b>Team</b><br><i><a href=https://www.linkedin.com/in/sutrinh/ style=color:orange>Sử Trịnh</a><br><a href=https://www.linkedin.com/in/jotaguy style=color:orange>Gia Hưng</a><br><a href=https://www.linkedin.com/in/hiepnguyendt style=color:orange>Thanh Hiệp </a></i></left><script async defer src=https://buttons.github.io/buttons.js></script></section></div></nav><section id=body><div id=overlay></div><div class="padding highlightable"><div><div id=top-bar><div id=breadcrumbs itemscope itemtype=http://data-vocabulary.org/Breadcrumb><span id=sidebar-toggle-span><a href=# id=sidebar-toggle data-sidebar-toggle><i class="fas fa-bars"></i>
</a></span><span id=toc-menu><i class="fas fa-list-alt"></i></span>
<span class=links><a href=/>Dynamic E-Commerce Website</a> > <a href=/5-deployfrontend/>Deploy Frontend</a> > Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</span></div><div class=progress><div class=wrapper><nav id=TableOfContents><ul><li><ul><li><a href=#enable-static-hosting-configure-cors-and-grant-permissions-for-the-s3-bucket>Enable Static Hosting, Configure CORS, and Grant Permissions for the S3 Bucket</a></li></ul></li></ul></nav></div></div></div></div><div id=head-tags></div><div id=body-inner><h1>Enable Static Hosting, Configure CORS, and Set S3 Bucket Permissions</h1><h3 id=enable-static-hosting-configure-cors-and-grant-permissions-for-the-s3-bucket>Enable Static Hosting, Configure CORS, and Grant Permissions for the S3 Bucket</h3><p>After uploading your frontend source code to the S3 Bucket, you need to <strong>enable static website hosting</strong>, set up the index and error documents, configure CORS policy, and grant public read permissions so your website runs stably, is accessible from browsers, and can connect to the backend API.</p><h4 id=step-1-enable-static-hosting-and-configure-cors>Step 1: Enable Static Hosting and Configure CORS</h4><ol><li><p><strong>Enable static website hosting for the bucket</strong></p><ul><li>Go to your frontend bucket in the <a href=https://s3.console.aws.amazon.com/s3/>AWS S3 Console</a>.</li><li>Select the <strong>Properties</strong> tab.</li><li>Scroll down to <strong>Static website hosting</strong> and click <strong>Edit</strong>.
<img alt=static_website_hosting src=/images/static_website_hosting.png></li><li>Select <strong>Enable</strong>.</li><li>Enter <code>index.html</code> in the <strong>Index document</strong> field.</li><li>You may also enter <code>index.html</code> for the <strong>Error document</strong> field (recommended for SPA deployments).</li><li>Save the settings.
<img alt=static_website_hosting src=/images/enable_static_website_hosting.png></li><li>AWS will generate a <strong>Website endpoint</strong> like:<pre tabindex=0><code>http://fcjfashionshop.com.s3-website-ap-southeast-1.amazonaws.com
</code></pre></li><li>Copy this URL for the next step.
<img alt=bucket_website_hosting src=/images/bucket_website_hosting.png></li></ul></li><li><p><strong>Configure CORS for the bucket</strong></p><ul><li>Go to the <strong>Permissions</strong> tab → scroll down to <strong>CORS configuration</strong> → click <strong>Edit</strong>.
<img alt=cors src=/images/cors.png></li><li>Add the following sample configuration (JSON) to allow the frontend to access resources or upload images to S3 from other domains:<div class=highlight><pre tabindex=0 style=color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4><code class=language-json data-lang=json><span style=display:flex><span>[
</span></span><span style=display:flex><span>  {
</span></span><span style=display:flex><span>    <span style=color:#f92672>&#34;AllowedOrigins&#34;</span>: [<span style=color:#e6db74>&#34;*&#34;</span>],
</span></span><span style=display:flex><span>    <span style=color:#f92672>&#34;AllowedMethods&#34;</span>: [<span style=color:#e6db74>&#34;GET&#34;</span>, <span style=color:#e6db74>&#34;HEAD&#34;</span>, <span style=color:#e6db74>&#34;PUT&#34;</span>, <span style=color:#e6db74>&#34;POST&#34;</span>, <span style=color:#e6db74>&#34;DELETE&#34;</span>],
</span></span><span style=display:flex><span>    <span style=color:#f92672>&#34;AllowedHeaders&#34;</span>: [<span style=color:#e6db74>&#34;*&#34;</span>]
</span></span><span style=display:flex><span>  }
</span></span><span style=display:flex><span>]
</span></span></code></pre></div></li><li>To restrict to a specific domain for better security:<div class=highlight><pre tabindex=0 style=color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4><code class=language-json data-lang=json><span style=display:flex><span>[
</span></span><span style=display:flex><span>  {
</span></span><span style=display:flex><span>    <span style=color:#f92672>&#34;AllowedOrigins&#34;</span>: [<span style=color:#e6db74>&#34;https://your-domain.com&#34;</span>],
</span></span><span style=display:flex><span>    <span style=color:#f92672>&#34;AllowedMethods&#34;</span>: [<span style=color:#e6db74>&#34;GET&#34;</span>, <span style=color:#e6db74>&#34;HEAD&#34;</span>],
</span></span><span style=display:flex><span>    <span style=color:#f92672>&#34;AllowedHeaders&#34;</span>: [<span style=color:#e6db74>&#34;*&#34;</span>]
</span></span><span style=display:flex><span>  }
</span></span><span style=display:flex><span>]
</span></span></code></pre></div></li><li>Click <strong>Save</strong> to apply.</li></ul></li></ol><h4 id=step-2-grant-public-permissions-to-the-s3-bucket>Step 2: Grant Public Permissions to the S3 Bucket</h4><ol><li><p><strong>Set permissions for the Frontend Bucket</strong></p><ul><li>Go to the <strong>Permissions</strong> tab of the bucket, scroll down to <strong>Bucket policy</strong> → <strong>Edit</strong>.
<img alt=click_edit_bucket_policy src=/images/click_edit_bucket_policy.png></li><li>Add a policy to allow public read access, for example:<div class=highlight><pre tabindex=0 style=color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4><code class=language-json data-lang=json><span style=display:flex><span>{
</span></span><span style=display:flex><span>  <span style=color:#f92672>&#34;Version&#34;</span>: <span style=color:#e6db74>&#34;2012-10-17&#34;</span>,
</span></span><span style=display:flex><span>  <span style=color:#f92672>&#34;Statement&#34;</span>: [
</span></span><span style=display:flex><span>    {
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Sid&#34;</span>: <span style=color:#e6db74>&#34;PublicReadGetObject&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Effect&#34;</span>: <span style=color:#e6db74>&#34;Allow&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Principal&#34;</span>: <span style=color:#e6db74>&#34;*&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Action&#34;</span>: <span style=color:#e6db74>&#34;s3:GetObject&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Resource&#34;</span>: <span style=color:#e6db74>&#34;arn:aws:s3:::fcjfashionshop.com/*&#34;</span>
</span></span><span style=display:flex><span>    }
</span></span><span style=display:flex><span>  ]
</span></span><span style=display:flex><span>}
</span></span></code></pre></div></li><li>Click <strong>Save</strong> to apply.
<img alt=edit_permission_bucket_avata src=/images/edit_permission_bucket_avata.png></li></ul></li><li><p><strong>Set permissions for the avatar upload bucket (if any)</strong></p><ul><li>Go to the <strong>Permissions</strong> tab of the bucket → <strong>Bucket policy</strong>.
<img alt=click_bucket_myfrontend src=/images/permission_upload_avata.png></li><li>Add a policy to allow public read access, for example:<div class=highlight><pre tabindex=0 style=color:#f8f8f2;background-color:#272822;-moz-tab-size:4;-o-tab-size:4;tab-size:4><code class=language-json data-lang=json><span style=display:flex><span>{
</span></span><span style=display:flex><span>  <span style=color:#f92672>&#34;Version&#34;</span>: <span style=color:#e6db74>&#34;2012-10-17&#34;</span>,
</span></span><span style=display:flex><span>  <span style=color:#f92672>&#34;Statement&#34;</span>: [
</span></span><span style=display:flex><span>    {
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Sid&#34;</span>: <span style=color:#e6db74>&#34;PublicReadGetObject&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Effect&#34;</span>: <span style=color:#e6db74>&#34;Allow&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Principal&#34;</span>: <span style=color:#e6db74>&#34;*&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Action&#34;</span>: <span style=color:#e6db74>&#34;s3:GetObject&#34;</span>,
</span></span><span style=display:flex><span>      <span style=color:#f92672>&#34;Resource&#34;</span>: <span style=color:#e6db74>&#34;arn:aws:s3:::uploads-avatars-2025/*&#34;</span>
</span></span><span style=display:flex><span>    }
</span></span><span style=display:flex><span>  ]
</span></span><span style=display:flex><span>}
</span></span></code></pre></div></li><li>Click <strong>Save</strong> to apply.
<img alt=edit_permission_bucket_avata src=/images/edit_permission_bucket_avata.png></li></ul></li></ol><h4 id=step-3-verify-website-operation>Step 3: Verify Website Operation</h4><ul><li>Use the <strong>Website endpoint</strong> link to test your website.</li><li>If <code>index.html</code> loads successfully, your website is running on S3.
<img alt=front_website_s3 src=/images/front_s3_website.png></li><li>If you encounter 403/404 errors:<ul><li>Double-check your bucket policy permissions.</li><li>Make sure static hosting is enabled.</li><li>Verify that your index/error file names are correct.</li></ul></li></ul><div class="notices warning"><p><strong>Note:</strong><br>You should only use <code>"*"</code> for development/testing. When deploying to production, always specify your real website domain in <code>AllowedOrigins</code> for better security.</p></div><p><strong>Conclusion:</strong><br>Properly enabling static hosting, configuring CORS, and setting public permissions will ensure your frontend website on S3 operates reliably, can call the backend API, and provides optimal load speed and user experience.</p><footer class=footline></footer></div></div><div id=navigation><a class="nav nav-prev" href=/5-deployfrontend/5.1-frontend-s3/ title="Deploy Frontend to S3 Bucket"><i class="fa fa-chevron-left"></i></a>
<a class="nav nav-next" href=/5-deployfrontend/5.3-s3-bucket-permission/ title="Grant Public Permissions to S3 Bucket" style=margin-right:0><i class="fa fa-chevron-right"></i></a></div></section><div style=left:-1000px;overflow:scroll;position:absolute;top:-1000px;border:none;box-sizing:content-box;height:200px;margin:0;padding:0;width:200px><div style=border:none;box-sizing:content-box;height:200px;margin:0;padding:0;width:200px></div></div><script src=/js/clipboard.min.js?1751838762></script><script src=/js/perfect-scrollbar.min.js?1751838762></script><script src=/js/perfect-scrollbar.jquery.min.js?1751838762></script><script src=/js/jquery.sticky.js?1751838762></script><script src=/js/featherlight.min.js?1751838762></script><script src=/js/highlight.pack.js?1751838762></script><script>hljs.initHighlightingOnLoad()</script><script src=/js/modernizr.custom-3.6.0.js?1751838762></script><script src=/js/learn.js?1751838762></script><script src=/js/hugo-learn.js?1751838762></script><link href=/mermaid/mermaid.css?1751838762 rel=stylesheet><script src=/mermaid/mermaid.js?1751838762></script><script>mermaid.initialize({startOnLoad:!0})</script><script>(function(e,t,n,s,o,i,a){e.GoogleAnalyticsObject=o,e[o]=e[o]||function(){(e[o].q=e[o].q||[]).push(arguments)},e[o].l=1*new Date,i=t.createElement(n),a=t.getElementsByTagName(n)[0],i.async=1,i.src=s,a.parentNode.insertBefore(i,a)})(window,document,"script","https://www.google-analytics.com/analytics.js","ga"),ga("create","UA-158079754-2","auto"),ga("send","pageview")</script></body></html>