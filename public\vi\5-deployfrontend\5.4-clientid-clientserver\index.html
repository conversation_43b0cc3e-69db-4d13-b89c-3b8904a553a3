<!doctype html><html lang=vi class="js csstransforms3d"><head><meta charset=utf-8><meta name=viewport content="width=device-width,initial-scale=1"><meta name=generator content="Hugo 0.134.3"><meta name=description content><meta name=author content="<EMAIL>"><link rel=icon href=/images/favicon.png type=image/png><title>Cấu hình Google OAuth2 Client ID và Client Secret :: AWS System Manager</title>
<link href=/css/nucleus.css?1751838762 rel=stylesheet><link href=/css/fontawesome-all.min.css?1751838762 rel=stylesheet><link href=/css/hybrid.css?1751838762 rel=stylesheet><link href=/css/featherlight.min.css?1751838762 rel=stylesheet><link href=/css/perfect-scrollbar.min.css?1751838762 rel=stylesheet><link href=/css/auto-complete.css?1751838762 rel=stylesheet><link href=/css/atom-one-dark-reasonable.css?1751838762 rel=stylesheet><link href=/css/theme.css?1751838762 rel=stylesheet><link href=/css/hugo-theme.css?1751838762 rel=stylesheet><link href=/css/theme-workshop.css?1751838762 rel=stylesheet><script src=/js/jquery-3.3.1.min.js?1751838762></script><style>:root #header+#content>#left>#rlblock_left{display:none!important}</style></head><body data-url=/vi/5-deployfrontend/5.4-clientid-clientserver/><nav id=sidebar class=showVisitedLinks><div id=header-wrapper><div id=header><a id=logo href=/><svg id="Layer_1" data-name="Layer 1" viewBox="0 0 60 30" width="30%"><defs><style>.cls-1{fill:#fff}.cls-2{fill:#f90;fill-rule:evenodd}</style></defs><title>AWS-Logo_White-Color</title><path class="cls-1" d="M14.09 10.85a4.7 4.7.0 00.19 1.48 7.73 7.73.0 00.54 **********.0 ***********.64.0 01-.32.49l-1 .7a.83.83.0 01-.***********.0 01-.49-.23 3.8 3.8.0 01-.6-.77q-.25-.42-.51-1a6.14 6.14.0 01-4.89 2.3 4.54 4.54.0 01-3.32-1.19 4.27 4.27.0 01-1.22-3.2 4.28 4.28.0 011.46-3.4A6.06 6.06.0 017.69 6.46a12.47 12.47.0 011.76.13q.92.13 1.91.36V5.73a3.65 3.65.0 00-.79-2.66A3.81 3.81.0 007.86 2.3a7.71 7.71.0 00-1.79.22 12.78 12.78.0 00-1.79.57 4.55 4.55.0 01-.58.22h-.26q-.35.0-.35-.52V2a1.09 1.09.0 01.12-.58 1.2 1.2.0 01.47-.35A10.88 10.88.0 015.77.32 10.19 10.19.0 018.36.0a6 6 0 014.35 1.35 5.49 5.49.0 011.38 4.09zM7.34 13.38a5.36 5.36.0 001.72-.31A3.63 3.63.0 0010.63 12 2.62 2.62.0 0011.19 11a5.63 5.63.0 00.16-1.44v-.7a14.35 14.35.0 00-1.53-.28 12.37 12.37.0 00-1.56-.1 3.84 3.84.0 00-2.47.67A2.34 2.34.0 005 11a2.35 2.35.0 00.61 1.76A2.4 2.4.0 007.34 13.38zm13.35 1.8a1 1 0 01-.64-.16 1.3 1.3.0 01-.35-.65L15.81 1.51a3 3 0 01-.15-.67.36.36.0 01.41-.41H17.7a1 1 0 01.65.16 1.4 1.4.0 01.33.65l2.79 11 2.59-11A1.17 1.17.0 0124.39.6a1.1 1.1.0 01.67-.16H26.4a1.1 1.1.0 01.67.16 1.17 1.17.0 01.32.65L30 12.39 32.88 1.25A1.39 1.39.0 0133.22.6a1 1 0 01.65-.16h1.54a.36.36.0 01.41.41 1.36 1.36.0 010 .26 3.64 3.64.0 01-.12.41l-4 12.86a1.3 1.3.0 01-.35.65 1 1 0 01-.64.16H29.25a1 1 0 01-.67-.17 1.26 1.26.0 01-.32-.67L25.67 3.64l-2.56 10.7a1.26 1.26.0 01-.32.67 1 1 0 01-.67.17zm21.36.44a11.28 11.28.0 01-2.56-.29 7.44 7.44.0 01-1.92-.67 1 1 0 01-.61-.93v-.84q0-.52.38-.52a.9.9.0 01.31.06l.42.17a8.77 8.77.0 001.83.58 9.78 9.78.0 002 .2 4.48 4.48.0 002.43-.55 1.76 1.76.0 00.86-1.57 1.61 1.61.0 00-.45-1.16A4.29 4.29.0 0043 9.22l-2.41-.76A5.15 5.15.0 0138 6.78a3.94 3.94.0 01-.83-2.41 3.7 3.7.0 01.45-1.85 4.47 4.47.0 011.19-1.37 5.27 5.27.0 011.7-.86A7.4 7.4.0 0142.6.0a8.87 8.87.0 011.12.07q.57.07 1.08.19t.95.26a4.27 4.27.0 01.7.29 1.59 1.59.0 01.49.41.94.94.0 01.15.55v.79q0 .52-.38.52a1.76 1.76.0 01-.64-.2 7.74 7.74.0 00-3.2-.64 4.37 4.37.0 00-2.21.47 1.6 1.6.0 00-.79 1.48 1.58 1.58.0 00.49 1.18 4.94 4.94.0 001.83.92L44.55 7a5.08 5.08.0 012.57 1.6A3.76 3.76.0 0147.9 11a4.21 4.21.0 01-.44 1.93 4.4 4.4.0 01-1.21 1.47 5.43 5.43.0 01-1.85.93A8.25 8.25.0 0142.05 15.62z"/><path class="cls-2" d="M45.19 23.81C39.72 27.85 31.78 30 25 30A36.64 36.64.0 01.22 20.57c-.51-.46-.06-1.09.56-.74A49.78 49.78.0 0025.53 26.4 49.23 49.23.0 0044.4 22.53C45.32 22.14 46.1 23.14 45.19 23.81z"/><path class="cls-2" d="M47.47 21.21c-.7-.9-4.63-.42-6.39-.21-.53.06-.62-.4-.14-.74 3.13-2.2 8.27-1.57 8.86-.83s-.16 5.89-3.09 8.35c-.45.38-.88.18-.68-.32C46.69 25.8 48.17 22.11 47.47 21.21z"/></svg></a></div><div class=searchbox><label for=search-by><i class="fas fa-search"></i></label>
<input data-search-input id=search-by type=search placeholder=Search...>
<span data-search-clear><i class="fas fa-times"></i></span></div><script type=text/javascript src=/js/lunr.min.js?1751838762></script><script type=text/javascript src=/js/auto-complete.js?1751838762></script><script type=text/javascript>var baseurl="https://thuananwork.github.io//vi"</script><script type=text/javascript src=/js/search.js?1751838762></script></div><div class=highlightable><ul class=topics><li data-nav-id=/vi/1-introduce/ title="Giới thiệu" class=dd-item><a href=/vi/1-introduce/><b>1. </b>Giới thiệu
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/2-prerequiste/ title="Chuẩn bị môi trường" class=dd-item><a href=/vi/2-prerequiste/><b>2 </b>Chuẩn bị môi trường
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/vi/2-prerequiste/2.1-installnodejs/ title="Cài đặt NodeJS" class=dd-item><a href=/vi/2-prerequiste/2.1-installnodejs/><b>2.1 </b>Cài đặt NodeJS
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/2-prerequiste/2.2-installyarn/ title="Cài đặt Yarn" class=dd-item><a href=/vi/2-prerequiste/2.2-installyarn/><b>2.2 </b>Cài đặt Yarn
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/2-prerequiste/2.3-installsamcli/ title="Cài đặt SAM CLI" class=dd-item><a href=/vi/2-prerequiste/2.3-installsamcli/><b>2.3 </b>Cài đặt SAM CLI
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/2-prerequiste/2.4-createiam/ title="Tạo tài khoản và cấu hình IAM" class=dd-item><a href=/vi/2-prerequiste/2.4-createiam/><b>2.4 </b>Tạo tài khoản và cấu hình IAM
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/2-prerequiste/2.5-create-google-oauth2/ title="Tạo Google OAuth2 Project" class=dd-item><a href=/vi/2-prerequiste/2.5-create-google-oauth2/><b>2.5 </b>Tạo Google OAuth2 Project
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/vi/3-deploybackend/ title="Triển khai backend" class=dd-item><a href=/vi/3-deploybackend/><b>3. </b>Triển khai backend
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/vi/3-deploybackend/3.1-deploy-backend/ title="Triển khai backend bằng SAM CLI/CloudFormation" class=dd-item><a href=/vi/3-deploybackend/3.1-deploy-backend/><b>3.1 </b>Triển khai backend bằng SAM CLI/CloudFormation
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/3-deploybackend/3.2-check-status-log-backend/ title="Kiểm tra trạng thái và log backend sau khi triển khai" class=dd-item><a href=/vi/3-deploybackend/3.2-check-status-log-backend/><b>3.2 </b>Kiểm tra trạng thái và log backend sau khi triển khai
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/vi/4-testbackendapi/ title="Kiểm thử API backend với Postman" class=dd-item><a href=/vi/4-testbackendapi/><b>4 </b>Kiểm thử API backend với Postman
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/vi/4-testbackendapi/4.1-endpoint-api-gateway/ title="Lấy endpoint API Gateway" class=dd-item><a href=/vi/4-testbackendapi/4.1-endpoint-api-gateway/><b>4.1 </b>Lấy endpoint API Gateway
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/4-testbackendapi/4.2-get-post-response/ title="Gửi request GET/POST để kiểm tra response backend" class=dd-item><a href=/vi/4-testbackendapi/4.2-get-post-response/><b>4.2 </b>Gửi request GET/POST để kiểm tra response backend
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/vi/5-deployfrontend/ title="Triển khai frontend" class="dd-item
parent"><a href=/vi/5-deployfrontend/><b>5 </b>Triển khai frontend
<i class="fas fa-check read-icon"></i></a><ul><li data-nav-id=/vi/5-deployfrontend/5.1-frontend-s3/ title="Triển khai frontend lên S3 Bucket" class=dd-item><a href=/vi/5-deployfrontend/5.1-frontend-s3/><b>5.1 </b>Triển khai frontend lên S3 Bucket
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/5-deployfrontend/5.2-enable-static-hosting/ title="Bật static hosting, cấu hình CORS" class=dd-item><a href=/vi/5-deployfrontend/5.2-enable-static-hosting/><b>5.2 </b>Bật static hosting, cấu hình CORS
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/5-deployfrontend/5.3-s3-bucket-permission/ title="Cấp quyền public cho S3 Bucket" class=dd-item><a href=/vi/5-deployfrontend/5.3-s3-bucket-permission/><b>5.3 </b>Cấp quyền public cho S3 Bucket
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/5-deployfrontend/5.4-clientid-clientserver/ title="Cấu hình Google OAuth2 Client ID và Client Secret" class="dd-item
active"><a href=/vi/5-deployfrontend/5.4-clientid-clientserver/><b>5.4 </b>Cấu hình Google OAuth2 Client ID và Client Secret
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/5-deployfrontend/5.5-connect-frontend-api-backend/ title="Kết nối frontend với API backend" class=dd-item><a href=/vi/5-deployfrontend/5.5-connect-frontend-api-backend/><b>5.5 </b>Kết nối frontend với API backend
<i class="fas fa-check read-icon"></i></a></li></ul></li><li data-nav-id=/vi/6-ssl-s3-static/ title="Thiết lập trang web SSL S3 Static" class=dd-item><a href=/vi/6-ssl-s3-static/><b>6. </b>Thiết lập trang web SSL S3 Static
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/7-demo/ title="Demo và chạy dự án" class=dd-item><a href=/vi/7-demo/><b>7. </b>Demo và chạy dự án
<i class="fas fa-check read-icon"></i></a></li><li data-nav-id=/vi/8-cleanup/ title="Dọn dẹp tài nguyên" class=dd-item><a href=/vi/8-cleanup/><b>8. </b>Dọn dẹp tài nguyên
<i class="fas fa-check read-icon"></i></a></li></ul><section id=shortcuts><h3>More</h3><ul><li><a class=padding href=https://www.facebook.com/groups/awsstudygroupfcj/><i class='fab fa-facebook'></i> AWS Study Group</a></li></ul></section><section id=prefooter><hr><ul><li><a class=padding><i class="fas fa-language fa-fw"></i><div class=select-style><select id=select-language onchange="location=this.value"><option id=en value=https://thuananwork.github.io/5-deployfrontend/5.4-clientid-clientserver/>English</option><option id=vi value=https://thuananwork.github.io/vi/5-deployfrontend/5.4-clientid-clientserver/ selected>Tiếng Việt</option></select><svg id="Capa_1" xmlns:xlink="http://www.w3.org/1999/xlink" width="255" height="255" viewBox="0 0 255 255" style="enable-background:new 0 0 255 255"><g><g id="arrow-drop-down"><polygon points="0,63.75 127.5,191.25 255,63.75"/></g></g></svg></div></a></li><li><a class=padding href=# data-clear-history-toggle><i class="fas fa-history fa-fw"></i> Clear History</a></li></ul></section><section id=footer><left><b>Workshop</b><br><img src="https://hitwebcounter.com/counter/counter.php?page=7920860&style=0038&nbdigits=9&type=page&initCount=0" title=Migrate alt="web counter" border=0></a><br><b><a href=https://cloudjourney.awsstudygroup.com/>Cloud Journey</a></b><br><img src="https://hitwebcounter.com/counter/counter.php?page=7830807&style=0038&nbdigits=9&type=page&initCount=0" title="Total CLoud Journey" alt="web counter" border=0>
</left><left><br><br><b>Last Updated</b><br><i><font color=orange>30-01-2022</font></i>
</left><left><br><br><b>Team</b><br><i><a href=https://www.linkedin.com/in/sutrinh/ style=color:orange>Sử Trịnh</a><br><a href=https://www.linkedin.com/in/jotaguy style=color:orange>Gia Hưng</a><br><a href=https://www.linkedin.com/in/hiepnguyendt style=color:orange>Thanh Hiệp </a></i></left><script async defer src=https://buttons.github.io/buttons.js></script></section></div></nav><section id=body><div id=overlay></div><div class="padding highlightable"><div><div id=top-bar><div id=breadcrumbs itemscope itemtype=http://data-vocabulary.org/Breadcrumb><span id=sidebar-toggle-span><a href=# id=sidebar-toggle data-sidebar-toggle><i class="fas fa-bars"></i>
</a></span><span id=toc-menu><i class="fas fa-list-alt"></i></span>
<span class=links><a href=/vi/>Website thương mại điện tử</a> > <a href=/vi/5-deployfrontend/>Triển khai frontend</a> > Cấu hình Google OAuth2 Client ID và Client Secret</span></div><div class=progress><div class=wrapper><nav id=TableOfContents><ul><li><ul><li></li></ul></li></ul></nav></div></div></div></div><div id=head-tags></div><div id=body-inner><h1>Cấu hình Google OAuth2 Client ID và Client Secret</h1><h4 id=cấu-hình-google-oauth2-client-id-và-client-secret>Cấu hình Google OAuth2 Client ID và Client Secret</h4><p>Sau khi đã tạo Google OAuth2 Project và bật các API cần thiết ở bước chuẩn bị môi trường, bạn cần tạo <strong>OAuth2 Client ID và Client Secret</strong> để tích hợp đăng nhập Google cho hệ thống thương mại điện tử.</p><p><strong>Các bước thực hiện:</strong></p><ol><li><p><strong>Truy cập vào Google Cloud Console</strong></p><ul><li>Mở <a href=https://console.cloud.google.com/>Google Cloud Console</a> và chọn đúng project đã tạo ở bước trước.
<img alt=api_services src=/images/api_services.png></li></ul></li><li><p><strong>Tạo OAuth2 Client ID</strong></p><ul><li><p>Vào <strong>APIs & Services</strong> → <strong>Credentials</strong>.
<img alt=credentials src=/images/credentials.png></p></li><li><p>Click vào <strong>+ Create Credentials</strong> → <strong>OAuth client ID</strong>.
<img alt=create_oAuth_client_id src=/images/oAuth_client_id.png></p></li><li><p>Click vào <strong>Configure consent srcreen</strong>.
<img alt=configure_consent_screen src=/images/configure_consent_screen.png></p></li><li><p>Click vào <strong>Get Started</strong>.
<img alt=get_started src=/images/get_started.png></p></li><li><p>Bạn điền các thông tin như sau:</p><ul><li><p>App name: <code>FcjFashionShop</code></p></li><li><p>User support email: <strong>Nhập email của bạn</strong></p></li><li><p>Nhấn vào <strong>Next</strong>
<img alt=information_google_oauth src=/images/information_google_oauth.png></p></li><li><p>Email addresses: <strong>Nhập email của bạn</strong>.</p></li><li><p>Nhấn vào <strong>Next</strong>
<img alt=contact_information src=/images/contact_information.png></p></li><li><p>Tích chọn <strong>I agree to the Google API Services: User Data Policy.</strong></p></li><li><p>Chọn <strong>Continue</strong> và chọn <strong>Create</strong>
<img alt=finish_create_oauth src=/images/finish_create_oauth.png></p></li></ul></li><li><p>Tại <strong>Metrics</strong> click vào <strong>Create OAuth client</strong>
<img alt=select_create_oauth_client src=/images/select_create_oauth_client.png></p></li><li><p>Tại Application type chọn <strong>Web application</strong></p></li><li><p>Name: <code>FcjFashionShop</code>
<img alt=info_oauth_client_id src=/images/info_oauth_client_id.png></p></li><li><p>Tại <strong>Authorized JavaScript origins</strong></p><ul><li>Chọn <strong>Add URI</strong> để thêm URl mới</li><li>Dán URL của <strong>S3 Bucket website endpoint</strong> bạn đã copy trước đó</li></ul></li><li><p>Tại <strong>Authorized redirect URIs</strong></p><ul><li>Chọn <strong>Add URI</strong> để thêm URl mới</li><li>Dán URL của <strong>Invoke URL của API Gateway</strong> bạn đã copy trước đó thay thế đoạn <strong>your-API-Gateway-domain</strong> trong command bên dưới</li><li>Nhấn <strong>create</strong></li></ul><pre tabindex=0><code>your-API-Gateway-domain/api/users/auth/google/callback
</code></pre></li></ul><p><img alt=create_oauth_client_id src=/images/create_oauth_client_id.png></p><ul><li><strong>ClientID</strong> và <strong>ClientSecret</strong> đã được tạo thành công, bạn hãy copy và lưu lại để dùng ở bước sau
<img alt=success_clientid_client_secret src=/images/success_clientid_client_secret.png></li></ul></li></ol><div class="notices info"><p>Authorized JavaScript origins: là domain frontend (S3 Static Website endpoint).
Authorized redirect URIs: là endpoint backend (API Gateway) xử lý callback Google.</p></div><div class="notices warning"><p><strong>Lưu ý bảo mật:</strong><br>Không public <strong>Client Secret</strong> lên Github hoặc bất cứ đâu!</p></div><p><strong>Kết luận:</strong><br>Sau khi hoàn thành các bước này, bạn đã có đủ thông tin để cấu hình Google OAuth2 cho cả backend và frontend, sẵn sàng triển khai tính năng đăng nhập Google cho dự án website thương mại điện tử trên AWS.</p><footer class=footline></footer></div></div><div id=navigation><a class="nav nav-prev" href=/vi/5-deployfrontend/5.3-s3-bucket-permission/ title="Cấp quyền public cho S3 Bucket"><i class="fa fa-chevron-left"></i></a>
<a class="nav nav-next" href=/vi/5-deployfrontend/5.5-connect-frontend-api-backend/ title="Kết nối frontend với API backend" style=margin-right:0><i class="fa fa-chevron-right"></i></a></div></section><div style=left:-1000px;overflow:scroll;position:absolute;top:-1000px;border:none;box-sizing:content-box;height:200px;margin:0;padding:0;width:200px><div style=border:none;box-sizing:content-box;height:200px;margin:0;padding:0;width:200px></div></div><script src=/js/clipboard.min.js?1751838762></script><script src=/js/perfect-scrollbar.min.js?1751838762></script><script src=/js/perfect-scrollbar.jquery.min.js?1751838762></script><script src=/js/jquery.sticky.js?1751838762></script><script src=/js/featherlight.min.js?1751838762></script><script src=/js/highlight.pack.js?1751838762></script><script>hljs.initHighlightingOnLoad()</script><script src=/js/modernizr.custom-3.6.0.js?1751838762></script><script src=/js/learn.js?1751838762></script><script src=/js/hugo-learn.js?1751838762></script><link href=/mermaid/mermaid.css?1751838762 rel=stylesheet><script src=/mermaid/mermaid.js?1751838762></script><script>mermaid.initialize({startOnLoad:!0})</script><script>(function(e,t,n,s,o,i,a){e.GoogleAnalyticsObject=o,e[o]=e[o]||function(){(e[o].q=e[o].q||[]).push(arguments)},e[o].l=1*new Date,i=t.createElement(n),a=t.getElementsByTagName(n)[0],i.async=1,i.src=s,a.parentNode.insertBefore(i,a)})(window,document,"script","https://www.google-analytics.com/analytics.js","ga"),ga("create","UA-158079754-2","auto"),ga("send","pageview")</script></body></html>