baseURL = "https://thuananwork.github.io/"
# Change the default theme to be use when building the site with Hugo
theme = "hugo-theme-learn"

# For search functionality
[outputs]
home = [ "HTML", "RSS", "JSON"]

[params]
  # Change default color scheme with a variant one. Can be "red", "blue", "green".
  themeVariant = "workshop"
  # Git hub repo link
  editURL = ""
  description = ""
  author = "<EMAIL>"
  showVisitedLinks = true
  disableBreadcrumb = false
  disableNextPrev = false
  
[Languages]
[Languages.en]
title = "AWS System Manager"
weight = 1
languageName = "English"

[[Languages.en.menu.shortcuts]]
name = "<i class='fab fa-facebook'></i> AWS Study Group"
identifier = "AWS Study Group FB"
url = "https://www.facebook.com/groups/awsstudygroupfcj/"
weight = 20

[Languages.vi]
title = "AWS System Manager"
weight = 2
languageName = "Tiếng Việt"

[[Languages.vi.menu.shortcuts]]
name = "<i class='fab fa-facebook'></i> AWS Study Group"
identifier = "AWS Study Group FB"
url = "https://www.facebook.com/groups/awsstudygroupfcj/"
weight = 20

