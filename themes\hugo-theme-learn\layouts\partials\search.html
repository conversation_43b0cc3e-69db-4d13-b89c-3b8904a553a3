<div class="searchbox">
    <label for="search-by"><i class="fas fa-search"></i></label>
    <input data-search-input id="search-by" type="search" placeholder="{{T "Search-placeholder"}}">
    <span data-search-clear=""><i class="fas fa-times"></i></span>
</div>
{{ $assetBusting := not .Site.Params.disableAssetsBusting }}
<script type="text/javascript" src="{{"js/lunr.min.js" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}"></script>
<script type="text/javascript" src="{{"js/auto-complete.js" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}"></script>
<script type="text/javascript">
    {{if .Site.IsMultiLingual}}
    var baseurl = "{{.Site.BaseURL}}{{.Site.LanguagePrefix}}";
    {{else}}
    var baseurl = "{{.Site.BaseURL}}";
    {{end}}
</script>
<script type="text/javascript" src="{{"js/search.js" | relURL}}{{ if $assetBusting }}?{{ now.Unix }}{{ end }}"></script>
